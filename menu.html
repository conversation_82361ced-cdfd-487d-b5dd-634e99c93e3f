<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Browse our full menu of authentic Nigerian dishes. From jollof rice to egusi soup, discover traditional flavors at Magic Menu.">
    <meta name="keywords" content="Nigerian menu, jollof rice, egusi soup, suya, African food menu, traditional dishes">
    <title>Menu - Magic Menu | Authentic Nigerian Cuisine</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="images/icons/favicon.ico">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" href="styles/base.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/pages.css">
    <link rel="stylesheet" href="styles/responsive.css">
</head>
<body>
    <!-- Skip Link for Accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Navigation -->
    <nav class="navbar" role="navigation" aria-label="Main navigation">
        <div class="container">
            <div class="navbar-container">
                <a href="index.html" class="logo" aria-label="Magic Menu - Go to homepage">
                    <img src="images/logos/logo.png" alt="Magic Menu Logo" width="40" height="40">
                    <span class="logo-text">Magic Menu</span>
                </a>
                
                <button class="navbar-toggle" type="button" aria-expanded="false" aria-controls="navbar-nav" aria-label="Toggle navigation menu">
                    ☰
                </button>
                
                <ul class="navbar-nav" id="navbar-nav">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="menu.html" class="active">Menu</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="contact.html">Contact</a></li>
                    <li><a href="account.html">Account</a></li>
                    <li>
                        <a href="cart.html" class="cart-link" aria-label="Shopping cart with 0 items">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 22C9.55228 22 10 21.5523 10 21C10 20.4477 9.55228 20 9 20C8.44772 20 8 20.4477 8 21C8 21.5523 8.44772 22 9 22Z"/>
                                <path d="M20 22C20.5523 22 21 21.5523 21 21C21 20.4477 20.5523 20 20 20C19.4477 20 19 20.4477 19 21C19 21.5523 19.4477 22 20 22Z"/>
                                <path d="M1 1H5L7.68 14.39C7.77144 14.8504 8.02191 15.264 8.38755 15.5583C8.75318 15.8526 9.2107 16.009 9.68 16H19.4C19.8693 16.009 20.3268 15.8526 20.6925 15.5583C21.0581 15.264 21.3086 14.8504 21.4 14.39L23 6H6"/>
                            </svg>
                            Cart <span class="cart-count">0</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content">
        <!-- Menu Header -->
        <section class="menu-header">
            <div class="container">
                <h1>Our Menu</h1>
                <p>Discover authentic Nigerian flavors crafted with love and tradition</p>
            </div>
        </section>

        <!-- Menu Content -->
        <section class="menu-content section">
            <div class="container">
                <!-- Menu Filters -->
                <div class="menu-filters" role="tablist" aria-label="Menu categories">
                    <button class="filter-btn active" data-category="all" role="tab" aria-selected="true">All Items</button>
                    <button class="filter-btn" data-category="rice-dishes" role="tab" aria-selected="false">Rice Dishes</button>
                    <button class="filter-btn" data-category="soups" role="tab" aria-selected="false">Soups</button>
                    <button class="filter-btn" data-category="grills" role="tab" aria-selected="false">Grills</button>
                    <button class="filter-btn" data-category="appetizers" role="tab" aria-selected="false">Appetizers</button>
                    <button class="filter-btn" data-category="main-dishes" role="tab" aria-selected="false">Main Dishes</button>
                    <button class="filter-btn" data-category="beverages" role="tab" aria-selected="false">Beverages</button>
                </div>

                <!-- Menu Categories -->
                <div id="menu-categories">
                    <!-- Categories will be loaded here by JavaScript -->
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer" role="contentinfo">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Magic Menu</h3>
                    <p>Bringing authentic Nigerian cuisine to your doorstep with love, tradition, and the finest ingredients.</p>
                    <div class="social-links">
                        <a href="#" aria-label="Follow us on Facebook">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"/>
                            </svg>
                        </a>
                        <a href="#" aria-label="Follow us on Instagram">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect x="2" y="2" width="20" height="20" rx="5" ry="5"/>
                                <path d="m16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"/>
                                <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"/>
                            </svg>
                        </a>
                        <a href="#" aria-label="Follow us on Twitter">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"/>
                            </svg>
                        </a>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="menu.html">Menu</a></li>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="contact.html">Contact</a></li>
                        <li><a href="faq.html">FAQ</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Customer Service</h4>
                    <ul>
                        <li><a href="account.html">My Account</a></li>
                        <li><a href="cart.html">Order Status</a></li>
                        <li><a href="privacy.html">Privacy Policy</a></li>
                        <li><a href="terms.html">Terms of Service</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <address>
                        <p>📍 123 Victoria Island, Lagos, Nigeria</p>
                        <p>📞 <a href="tel:+*************">+234 ************</a></p>
                        <p>✉️ <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    </address>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Magic Menu. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/menu-data.js"></script>
    <script>
        // Menu functionality
        let currentFilter = 'all';

        // Load menu data (now from the included JavaScript file)
        function loadMenuData() {
            try {
                // menuData is now available from menu-data.js
                renderMenu();
            } catch (error) {
                console.error('Error loading menu data:', error);
                document.getElementById('menu-categories').innerHTML = `
                    <div class="alert alert-error">
                        <p>Sorry, we're having trouble loading the menu. Please try again later.</p>
                    </div>
                `;
            }
        }

        // Render menu based on current filter
        function renderMenu() {
            if (!menuData) return;

            const container = document.getElementById('menu-categories');
            let categoriesToShow = menuData.categories;
            let itemsToShow = menuData.items;

            // Filter items if not showing all
            if (currentFilter !== 'all') {
                itemsToShow = menuData.items.filter(item => item.category === currentFilter);
                categoriesToShow = menuData.categories.filter(cat => cat.id === currentFilter);
            }

            // Group items by category
            const itemsByCategory = {};
            itemsToShow.forEach(item => {
                if (!itemsByCategory[item.category]) {
                    itemsByCategory[item.category] = [];
                }
                itemsByCategory[item.category].push(item);
            });

            // Render categories and items
            container.innerHTML = categoriesToShow.map(category => {
                const categoryItems = itemsByCategory[category.id] || [];
                if (categoryItems.length === 0) return '';

                return `
                    <div class="menu-category" id="category-${category.id}">
                        <div class="category-header">
                            <h2 class="category-title">${category.name}</h2>
                            <p class="category-description">${category.description}</p>
                        </div>
                        <div class="menu-items">
                            ${categoryItems.map(item => `
                                <div class="menu-item" data-category="${item.category}">
                                    ${item.isPopular ? '<div class="popular-badge">Popular</div>' : ''}
                                    <div class="menu-item-image">
                                        <img src="${item.image}" alt="${item.name}" loading="lazy">
                                    </div>
                                    <div class="menu-item-content">
                                        <div class="menu-item-header">
                                            <h3 class="menu-item-title">${item.name}</h3>
                                            <div class="menu-item-meta">
                                                <span class="menu-item-category">${category.name}</span>
                                                ${item.spiceLevel && item.spiceLevel !== 'none' ? 
                                                    `<span class="spice-level ${item.spiceLevel}">${item.spiceLevel}</span>` : ''}
                                            </div>
                                        </div>
                                        <p class="menu-item-description">${item.description}</p>
                                        <div class="menu-item-footer">
                                            <div class="menu-item-price">₦${item.price.toLocaleString()}</div>
                                            <button class="btn btn-primary add-to-cart" 
                                                    data-item-id="${item.id}"
                                                    data-item-name="${item.name}"
                                                    data-item-price="${item.price}"
                                                    data-item-image="${item.image}"
                                                    data-item-category="${item.category}"
                                                    ${!item.isAvailable ? 'disabled' : ''}>
                                                ${item.isAvailable ? 'Add to Cart' : 'Unavailable'}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }).join('');
        }

        // Handle filter button clicks
        function setupFilters() {
            const filterButtons = document.querySelectorAll('.filter-btn');
            
            filterButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Update active state
                    filterButtons.forEach(btn => {
                        btn.classList.remove('active');
                        btn.setAttribute('aria-selected', 'false');
                    });
                    button.classList.add('active');
                    button.setAttribute('aria-selected', 'true');
                    
                    // Update filter and re-render
                    currentFilter = button.dataset.category;
                    renderMenu();
                });
            });
        }

        // Initialize menu page
        document.addEventListener('DOMContentLoaded', () => {
            loadMenuData();
            setupFilters();
        });
    </script>
</body>
</html>
