<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Complete your order at Magic Menu. Enter your delivery details and payment information to enjoy authentic Nigerian cuisine.">
    <meta name="keywords" content="checkout, order, payment, delivery, Nigerian food order">
    <title>Checkout - Magic Menu | Complete Your Order</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="images/icons/favicon.ico">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" href="styles/base.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/pages.css">
    <link rel="stylesheet" href="styles/responsive.css">
</head>
<body>
    <!-- Skip Link for Accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Navigation -->
    <nav class="navbar" role="navigation" aria-label="Main navigation">
        <div class="container">
            <div class="navbar-container">
                <a href="index.html" class="logo" aria-label="Magic Menu - Go to homepage">
                    <img src="images/logos/logo.png" alt="Magic Menu Logo" width="40" height="40">
                    <span class="logo-text">Magic Menu</span>
                </a>
                
                <button class="navbar-toggle" type="button" aria-expanded="false" aria-controls="navbar-nav" aria-label="Toggle navigation menu">
                    ☰
                </button>
                
                <ul class="navbar-nav" id="navbar-nav">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="menu.html">Menu</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="contact.html">Contact</a></li>
                    <li><a href="account.html">Account</a></li>
                    <li>
                        <a href="cart.html" class="cart-link" aria-label="Shopping cart with 0 items">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 22C9.55228 22 10 21.5523 10 21C10 20.4477 9.55228 20 9 20C8.44772 20 8 20.4477 8 21C8 21.5523 8.44772 22 9 22Z"/>
                                <path d="M20 22C20.5523 22 21 21.5523 21 21C21 20.4477 20.5523 20 20 20C19.4477 20 19 20.4477 19 21C19 21.5523 19.4477 22 20 22Z"/>
                                <path d="M1 1H5L7.68 14.39C7.77144 14.8504 8.02191 15.264 8.38755 15.5583C8.75318 15.8526 9.2107 16.009 9.68 16H19.4C19.8693 16.009 20.3268 15.8526 20.6925 15.5583C21.0581 15.264 21.3086 14.8504 21.4 14.39L23 6H6"/>
                            </svg>
                            Cart <span class="cart-count">0</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content">
        <!-- Page Header -->
        <section class="page-header">
            <div class="container">
                <h1>Checkout</h1>
                <p>Complete your order and get ready to enjoy authentic Nigerian cuisine</p>
            </div>
        </section>

        <!-- Checkout Content -->
        <section class="checkout-section section">
            <div class="container">
                <div class="checkout-container">
                    <!-- Checkout Form -->
                    <div class="checkout-form">
                        <form id="checkout-form" data-validate data-form-type="checkout">
                            <!-- Customer Information -->
                            <div class="form-section">
                                <h2 class="form-section-title">Customer Information</h2>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="firstName" class="form-label">First Name *</label>
                                        <input type="text" id="firstName" name="firstName" class="form-control" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="lastName" class="form-label">Last Name *</label>
                                        <input type="text" id="lastName" name="lastName" class="form-control" required>
                                    </div>
                                </div>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="email" class="form-label">Email Address *</label>
                                        <input type="email" id="email" name="email" class="form-control" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="phone" class="form-label">Phone Number *</label>
                                        <input type="tel" id="phone" name="phone" class="form-control" required placeholder="+234 ************">
                                    </div>
                                </div>
                            </div>

                            <!-- Delivery Information -->
                            <div class="form-section">
                                <h2 class="form-section-title">Delivery Information</h2>
                                
                                <div class="form-group">
                                    <label for="address" class="form-label">Street Address *</label>
                                    <input type="text" id="address" name="address" class="form-control" required placeholder="Enter your full address">
                                </div>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="city" class="form-label">City *</label>
                                        <select id="city" name="city" class="form-control" required>
                                            <option value="">Select City</option>
                                            <option value="lagos">Lagos</option>
                                            <option value="abuja">Abuja</option>
                                        </select>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="area" class="form-label">Area *</label>
                                        <select id="area" name="area" class="form-control" required>
                                            <option value="">Select Area</option>
                                            <option value="victoria-island">Victoria Island</option>
                                            <option value="ikoyi">Ikoyi</option>
                                            <option value="lekki">Lekki</option>
                                            <option value="ajah">Ajah</option>
                                            <option value="surulere">Surulere</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="deliveryInstructions" class="form-label">Delivery Instructions</label>
                                    <textarea id="deliveryInstructions" name="deliveryInstructions" class="form-control" rows="3" placeholder="Any special instructions for delivery (e.g., gate code, landmark, etc.)"></textarea>
                                </div>
                                
                                <div class="form-group">
                                    <label for="deliveryTime" class="form-label">Preferred Delivery Time</label>
                                    <select id="deliveryTime" name="deliveryTime" class="form-control">
                                        <option value="asap">As soon as possible</option>
                                        <option value="30min">In 30 minutes</option>
                                        <option value="1hour">In 1 hour</option>
                                        <option value="2hours">In 2 hours</option>
                                        <option value="custom">Custom time</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Payment Information -->
                            <div class="form-section">
                                <h2 class="form-section-title">Payment Information</h2>
                                
                                <div class="payment-methods">
                                    <label class="payment-method">
                                        <input type="radio" name="paymentMethod" value="card" checked>
                                        <span class="payment-option">
                                            <span class="payment-icon">💳</span>
                                            <span class="payment-text">Credit/Debit Card</span>
                                        </span>
                                    </label>
                                    
                                    <label class="payment-method">
                                        <input type="radio" name="paymentMethod" value="transfer">
                                        <span class="payment-option">
                                            <span class="payment-icon">🏦</span>
                                            <span class="payment-text">Bank Transfer</span>
                                        </span>
                                    </label>
                                    
                                    <label class="payment-method">
                                        <input type="radio" name="paymentMethod" value="cash">
                                        <span class="payment-option">
                                            <span class="payment-icon">💵</span>
                                            <span class="payment-text">Cash on Delivery</span>
                                        </span>
                                    </label>
                                </div>
                                
                                <div id="card-details" class="card-details">
                                    <div class="form-group">
                                        <label for="cardNumber" class="form-label">Card Number *</label>
                                        <input type="text" id="cardNumber" name="cardNumber" class="form-control" placeholder="1234 5678 9012 3456">
                                    </div>
                                    
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="expiryDate" class="form-label">Expiry Date *</label>
                                            <input type="text" id="expiryDate" name="expiryDate" class="form-control" placeholder="MM/YY">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="cvv" class="form-label">CVV *</label>
                                            <input type="text" id="cvv" name="cvv" class="form-control" placeholder="123">
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="cardName" class="form-label">Name on Card *</label>
                                        <input type="text" id="cardName" name="cardName" class="form-control" placeholder="John Doe">
                                    </div>
                                </div>
                            </div>

                            <!-- Order Notes -->
                            <div class="form-section">
                                <h2 class="form-section-title">Additional Notes</h2>
                                <div class="form-group">
                                    <label for="orderNotes" class="form-label">Special Requests</label>
                                    <textarea id="orderNotes" name="orderNotes" class="form-control" rows="3" placeholder="Any special requests for your order (e.g., extra spicy, no onions, etc.)"></textarea>
                                </div>
                            </div>

                            <!-- Terms and Conditions -->
                            <div class="form-section">
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="terms" required>
                                        <span class="checkmark"></span>
                                        I agree to the <a href="terms.html" target="_blank">Terms of Service</a> and <a href="privacy.html" target="_blank">Privacy Policy</a> *
                                    </label>
                                </div>
                                
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="newsletter">
                                        <span class="checkmark"></span>
                                        Subscribe to our newsletter for special offers and updates
                                    </label>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary btn-lg btn-block">Place Order</button>
                        </form>
                    </div>

                    <!-- Order Summary -->
                    <div class="order-summary">
                        <h2>Order Summary</h2>
                        <div id="order-items">
                            <!-- Order items will be loaded here -->
                        </div>
                        
                        <div class="order-totals">
                            <div class="summary-line">
                                <span>Subtotal:</span>
                                <span id="subtotal">₦0</span>
                            </div>
                            <div class="summary-line">
                                <span>VAT (7.5%):</span>
                                <span id="tax">₦0</span>
                            </div>
                            <div class="summary-line">
                                <span>Delivery Fee:</span>
                                <span id="delivery-fee">₦0</span>
                            </div>
                            <div class="summary-line total">
                                <span><strong>Total:</strong></span>
                                <span id="total"><strong>₦0</strong></span>
                            </div>
                        </div>
                        
                        <div class="delivery-info">
                            <h3>Estimated Delivery</h3>
                            <p><strong>30-45 minutes</strong></p>
                            <p>We'll send you updates via SMS and email</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer" role="contentinfo">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Magic Menu</h3>
                    <p>Bringing authentic Nigerian cuisine to your doorstep with love, tradition, and the finest ingredients.</p>
                </div>
                
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="menu.html">Menu</a></li>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="contact.html">Contact</a></li>
                        <li><a href="faq.html">FAQ</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Customer Service</h4>
                    <ul>
                        <li><a href="account.html">My Account</a></li>
                        <li><a href="privacy.html">Privacy Policy</a></li>
                        <li><a href="terms.html">Terms of Service</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <address>
                        <p>📍 123 Victoria Island, Lagos, Nigeria</p>
                        <p>📞 <a href="tel:+*************">+234 ************</a></p>
                        <p>✉️ <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    </address>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Magic Menu. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/forms.js"></script>
    <script src="js/utils.js"></script>
    <script>
        // Checkout page functionality
        document.addEventListener('DOMContentLoaded', () => {
            loadOrderSummary();
            setupPaymentMethods();
            
            // Redirect if cart is empty
            if (Cart.items.length === 0) {
                MagicMenu.showToast('Your cart is empty. Redirecting to menu...', 'info');
                setTimeout(() => {
                    window.location.href = 'menu.html';
                }, 2000);
            }
        });

        function loadOrderSummary() {
            const cartData = Cart.getCartData();
            const orderItemsContainer = document.getElementById('order-items');
            
            if (cartData.items.length === 0) return;
            
            // Render order items
            orderItemsContainer.innerHTML = cartData.items.map(item => `
                <div class="order-item">
                    <div class="order-item-details">
                        <h4>${item.name}</h4>
                        <div class="order-item-meta">Qty: ${item.quantity}</div>
                    </div>
                    <div class="order-item-price">${Cart.formatCurrency(item.price * item.quantity)}</div>
                </div>
            `).join('');
            
            // Update totals
            document.getElementById('subtotal').textContent = Cart.formatCurrency(cartData.totals.subtotal);
            document.getElementById('tax').textContent = Cart.formatCurrency(cartData.totals.tax);
            document.getElementById('delivery-fee').textContent = Cart.formatCurrency(cartData.totals.deliveryFee);
            document.getElementById('total').textContent = Cart.formatCurrency(cartData.totals.total);
        }

        function setupPaymentMethods() {
            const paymentMethods = document.querySelectorAll('input[name="paymentMethod"]');
            const cardDetails = document.getElementById('card-details');
            
            paymentMethods.forEach(method => {
                method.addEventListener('change', () => {
                    if (method.value === 'card') {
                        cardDetails.style.display = 'block';
                        // Make card fields required
                        cardDetails.querySelectorAll('input').forEach(input => {
                            input.setAttribute('required', '');
                        });
                    } else {
                        cardDetails.style.display = 'none';
                        // Remove required from card fields
                        cardDetails.querySelectorAll('input').forEach(input => {
                            input.removeAttribute('required');
                        });
                    }
                });
            });
        }
    </script>
</body>
</html>
