<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Verify your Magic Menu email address to activate your account.">
    <title>Email Verification - Magic Menu</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="images/icons/favicon.ico">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" href="styles/base.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/pages.css">
    <link rel="stylesheet" href="styles/responsive.css">
</head>
<body>
    <!-- Skip Link for Accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Navigation -->
    <nav class="navbar" role="navigation" aria-label="Main navigation">
        <div class="container">
            <div class="navbar-container">
                <a href="index.html" class="logo" aria-label="Magic Menu - Go to homepage">
                    <img src="images/logos/logo.png" alt="Magic Menu Logo" width="40" height="40">
                    <span class="logo-text">Magic Menu</span>
                </a>
                
                <button class="navbar-toggle" type="button" aria-expanded="false" aria-controls="navbar-nav" aria-label="Toggle navigation menu">
                    ☰
                </button>
                
                <ul class="navbar-nav" id="navbar-nav">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="menu.html">Menu</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="contact.html">Contact</a></li>
                    <li><a href="account.html">Account</a></li>
                    <li>
                        <a href="cart.html" class="cart-link" aria-label="Shopping cart with 0 items">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 22C9.55228 22 10 21.5523 10 21C10 20.4477 9.55228 20 9 20C8.44772 20 8 20.4477 8 21C8 21.5523 8.44772 22 9 22Z"/>
                                <path d="M20 22C20.5523 22 21 21.5523 21 21C21 20.4477 20.5523 20 20 20C19.4477 20 19 20.4477 19 21C19 21.5523 19.4477 22 20 22Z"/>
                                <path d="M1 1H5L7.68 14.39C7.77144 14.8504 8.02191 15.264 8.38755 15.5583C8.75318 15.8526 9.2107 16.009 9.68 16H19.4C19.8693 16.009 20.3268 15.8526 20.6925 15.5583C21.0581 15.264 21.3086 14.8504 21.4 14.39L23 6H6"/>
                            </svg>
                            Cart <span class="cart-count">0</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content">
        <!-- Page Header -->
        <section class="page-header">
            <div class="container">
                <h1>Email Verification</h1>
                <p>Verifying your email address...</p>
            </div>
        </section>

        <!-- Verification Content -->
        <section class="verification-section section">
            <div class="container">
                <div class="verification-container">
                    <!-- Loading State -->
                    <div id="verification-loading" class="verification-state">
                        <div class="verification-icon">
                            <div class="loading-spinner"></div>
                        </div>
                        <h2>Verifying your email...</h2>
                        <p>Please wait while we verify your email address.</p>
                    </div>

                    <!-- Success State -->
                    <div id="verification-success" class="verification-state hidden">
                        <div class="verification-icon success">
                            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="12" cy="12" r="10" fill="#28a745"/>
                                <path d="M9 12l2 2 4-4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <h2>Email Verified Successfully!</h2>
                        <p>Your email address has been verified. You can now access all features of your Magic Menu account.</p>
                        <div class="verification-actions">
                            <a href="account.html" class="btn btn-primary">Go to Account</a>
                            <a href="menu.html" class="btn btn-secondary">Browse Menu</a>
                        </div>
                    </div>

                    <!-- Error State -->
                    <div id="verification-error" class="verification-state hidden">
                        <div class="verification-icon error">
                            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="12" cy="12" r="10" fill="#dc3545"/>
                                <path d="M15 9l-6 6M9 9l6 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <h2>Verification Failed</h2>
                        <p id="verification-error-message">The verification link is invalid or has expired.</p>
                        <div class="verification-actions">
                            <button type="button" class="btn btn-primary" id="resend-verification-btn">Send New Verification Email</button>
                            <a href="account.html" class="btn btn-secondary">Back to Account</a>
                        </div>
                    </div>

                    <!-- Resend Success State -->
                    <div id="resend-success" class="verification-state hidden">
                        <div class="verification-icon info">
                            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="12" cy="12" r="10" fill="#17a2b8"/>
                                <path d="M12 16v-4M12 8h.01" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <h2>Verification Email Sent</h2>
                        <p>A new verification email has been sent to your email address. Please check your inbox and click the verification link.</p>
                        <div class="verification-actions">
                            <a href="account.html" class="btn btn-primary">Back to Account</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer" role="contentinfo">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Magic Menu</h3>
                    <p>Bringing authentic Nigerian cuisine to your doorstep with love, tradition, and the finest ingredients.</p>
                </div>
                
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="menu.html">Menu</a></li>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="contact.html">Contact</a></li>
                        <li><a href="faq.html">FAQ</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Customer Service</h4>
                    <ul>
                        <li><a href="privacy.html">Privacy Policy</a></li>
                        <li><a href="terms.html">Terms of Service</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <address>
                        <p>📍 123 Victoria Island, Lagos, Nigeria</p>
                        <p>📞 <a href="tel:+2348012345678">+234 ************</a></p>
                        <p>✉️ <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    </address>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Magic Menu. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/utils.js"></script>
    <script src="js/main.js"></script>
    <script>
        // Email verification page functionality
        document.addEventListener('DOMContentLoaded', () => {
            handleEmailVerification();
        });

        function handleEmailVerification() {
            const urlParams = new URLSearchParams(window.location.search);
            const token = urlParams.get('token');

            if (!token) {
                showVerificationError('No verification token provided.');
                return;
            }

            // Clean up expired tokens first
            EmailVerification.cleanupExpiredTokens();

            // Simulate verification delay
            setTimeout(() => {
                const result = EmailVerification.verifyToken(token);
                
                if (result.success) {
                    showVerificationSuccess();
                    
                    // Update any open account pages
                    if (typeof BroadcastChannel !== 'undefined') {
                        const channel = new BroadcastChannel('magic-menu-updates');
                        channel.postMessage({
                            type: 'EMAIL_VERIFIED',
                            email: result.email,
                            timestamp: new Date().toISOString()
                        });
                    }
                } else {
                    showVerificationError(result.error);
                }
            }, 1500);
        }

        function showVerificationSuccess() {
            document.getElementById('verification-loading').classList.add('hidden');
            document.getElementById('verification-success').classList.remove('hidden');
            
            // Update page title and description
            document.title = 'Email Verified - Magic Menu';
            const metaDescription = document.querySelector('meta[name="description"]');
            if (metaDescription) {
                metaDescription.content = 'Your email has been successfully verified. Welcome to Magic Menu!';
            }
        }

        function showVerificationError(errorMessage) {
            document.getElementById('verification-loading').classList.add('hidden');
            document.getElementById('verification-error').classList.remove('hidden');
            document.getElementById('verification-error-message').textContent = errorMessage;
            
            // Setup resend button
            const resendBtn = document.getElementById('resend-verification-btn');
            resendBtn.addEventListener('click', handleResendVerification);
        }

        function handleResendVerification() {
            const user = Utils.storage.get('user');
            if (!user || !user.email) {
                MagicMenu.showToast('Please log in to resend verification email.', 'error');
                window.location.href = 'account.html';
                return;
            }

            const result = EmailVerification.resendVerification(user.email);
            
            if (result.success) {
                document.getElementById('verification-error').classList.add('hidden');
                document.getElementById('resend-success').classList.remove('hidden');
                MagicMenu.showToast('Verification email sent successfully!', 'success');
            } else {
                MagicMenu.showToast(result.error, 'error');
            }
        }
    </script>
</body>
</html>